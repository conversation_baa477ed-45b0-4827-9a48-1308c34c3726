/// Google OAuth configuration
/// 
/// <PERSON><PERSON> sử dụng Google Sign In, bạn cần:
/// 1. Tạo project trên Google Cloud Console
/// 2. Enable Google Sign-In API
/// 3. Tạo OAuth 2.0 credentials cho Web, iOS, và Android
/// 4. C<PERSON>p nhật các client IDs dưới đây
/// 
/// Hướng dẫn chi tiết:
/// - Web: https://developers.google.com/identity/sign-in/web/sign-in
/// - iOS: https://developers.google.com/identity/sign-in/ios/start-integrating
/// - Android: https://developers.google.com/identity/sign-in/android/start-integrating
class GoogleConfig {
  // Web Client ID từ google-services.json (client_type: 3)
  // L<PERSON>y từ Google Cloud Console > APIs & Services > Credentials > OAuth 2.0 Client IDs > Web application
  // ⚠️ QUAN TRỌNG: Đ<PERSON>y phải là Web Client ID, KHÔNG PHẢI iOS Client ID
  static const String webClientId = '315893752473-pqdfe9brhpm1ji0cbq0jlgu9335dd0mi.apps.googleusercontent.com';

  // iOS Client ID thật từ project naugiday (theo hướng dẫn Supabase chính thức)
  // Theo hướng dẫn: "Google sign in on Android will work without providing the Android Client ID"
  static const String iosClientId = '52267330277-89qhn7vafttk218og8mphf0jlaa3u2mm.apps.googleusercontent.com';
  
  // Android Client ID không cần thiết khi sử dụng google_sign_in package
  // Package sẽ tự động sử dụng SHA-1 fingerprint để xác thực
  
  /// Kiểm tra xem có cấu hình Google OAuth chưa
  static bool get isConfigured {
    return webClientId != 'YOUR_WEB_CLIENT_ID_HERE.apps.googleusercontent.com' &&
           iosClientId != 'YOUR_IOS_CLIENT_ID.apps.googleusercontent.com' &&
           webClientId.isNotEmpty &&
           iosClientId.isNotEmpty &&
           webClientId.contains('.apps.googleusercontent.com') &&
           iosClientId.contains('.apps.googleusercontent.com');
  }
  
  /// Thông báo lỗi khi chưa cấu hình
  static String get configurationError {
    if (!isConfigured) {
      return '''
Google OAuth chưa được cấu hình!

Vui lòng:
1. Tạo project trên Google Cloud Console
2. Enable Google Sign-In API  
3. Tạo OAuth 2.0 credentials
4. Cập nhật client IDs trong lib/config/google_config.dart

Hướng dẫn: https://supabase.com/blog/flutter-authentication
''';
    }
    return '';
  }
}
