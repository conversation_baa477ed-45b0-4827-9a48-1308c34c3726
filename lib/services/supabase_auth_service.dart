import 'dart:async';
import 'package:flutter/services.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../supabase_options.dart';
import '../config/google_config.dart';

class SupabaseAuthService {
  late final SupabaseClient _supabaseClient;

  SupabaseAuthService() {
    try {
      _supabaseClient = Supabase.instance.client;
      print('SupabaseAuthService: Đã khởi tạo Supabase client');
    } catch (e) {
      print('SupabaseAuthService: Lỗi khởi tạo: $e');
      rethrow;
    }
  }

  // Validate email
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // Validate password
  bool _isValidPassword(String password) {
    return password.length >= 6;
  }

  /// Translate common English error messages to friendly Vietnamese
  String _translateErrorMessage(String englishMessage) {
    final message = englishMessage.toLowerCase();
    
    // Common rate limit messages
    if (message.contains('too many requests') || 
        message.contains('rate limit') ||
        message.contains('too many emails')) {
      return 'Bạn đã thử quá nhiều lần. Hãy nghỉ một chút rồi thử lại nhé! 😊';
    }
    
    // Authentication errors
    if (message.contains('authentication failed') ||
        message.contains('auth failed')) {
      return 'Có lỗi xác thực. Vui lòng thử lại sau.';
    }
    
    // Network/timeout errors
    if (message.contains('timeout') || 
        message.contains('request timeout')) {
      return 'Kết nối hơi chậm. Vui lòng kiểm tra mạng và thử lại.';
    }
    
    // Server errors
    if (message.contains('internal server error') ||
        message.contains('server error') ||
        message.contains('service unavailable')) {
      return 'Máy chủ đang bận. Vui lòng thử lại sau vài phút.';
    }
    
    // Email specific errors
    if (message.contains('email') && message.contains('invalid')) {
      return 'Email không đúng định dạng. Vui lòng kiểm tra lại.';
    }
    
    // Generic fallback with friendly tone
    return 'Có chút vấn đề nhỏ xảy ra. Vui lòng thử lại sau nhé! 🙏';
  }

  User? get currentUser => _supabaseClient.auth.currentUser;
  Stream<AuthState> get authStateChanges =>
      _supabaseClient.auth.onAuthStateChange;

  bool isSignedIn() {
    return _supabaseClient.auth.currentUser != null;
  }

  // Đăng nhập bằng Google theo hướng dẫn chính thức Supabase
  Future<Map<String, dynamic>> signInWithGoogle() async {
    print('🚀 =================================');
    print('🚀 BẮT ĐẦU ĐĂNG NHẬP GOOGLE (SUPABASE OFFICIAL)');
    print('🚀 =================================');

    try {
      // 1. Kiểm tra cấu hình Google OAuth
      print('📋 STEP 1: Kiểm tra cấu hình Google OAuth');

      if (!GoogleConfig.isConfigured) {
        print('   ❌ Google OAuth chưa được cấu hình');
        return {
          'success': false,
          'message': GoogleConfig.configurationError,
          'debug': 'Google OAuth not configured'
        };
      }

      print('   ✓ Google OAuth đã được cấu hình');

      // 2. Khởi tạo Google Sign In
      print('📋 STEP 2: Khởi tạo Google Sign In');

      final GoogleSignIn googleSignIn = GoogleSignIn(
        clientId: GoogleConfig.iosClientId,
        serverClientId: GoogleConfig.webClientId,
        scopes: ['email', 'profile'],
      );

      print('   ✓ GoogleSignIn configuration:');
      print('      - clientId: ${GoogleConfig.iosClientId}');
      print('      - serverClientId: ${GoogleConfig.webClientId}');
      print('      - scopes: [email, profile]');

      print('   ✓ Google Sign In initialized');
      print('   ✓ Web Client ID: ${GoogleConfig.webClientId.substring(0, 20)}...');
      print('   ✓ iOS Client ID: ${GoogleConfig.iosClientId.substring(0, 20)}...');

      // 2. Thực hiện Google Sign In
      print('📋 STEP 2: Thực hiện Google Sign In');
      print('   ⏳ Đang gọi googleSignIn.signIn()...');

      GoogleSignInAccount? googleUser;
      try {
        googleUser = await googleSignIn.signIn();
        print('   ✓ googleSignIn.signIn() completed');
      } catch (signInError) {
        print('   ❌ googleSignIn.signIn() failed: $signInError');
        print('   ❌ Error type: ${signInError.runtimeType}');

        // Log chi tiết cho PlatformException
        if (signInError is PlatformException) {
          print('   🔍 PlatformException chi tiết:');
          print('      - Code: ${signInError.code}');
          print('      - Message: ${signInError.message}');
          print('      - Details: ${signInError.details}');

          // Giải thích các mã lỗi phổ biến
          switch (signInError.code) {
            case 'sign_in_failed':
              if (signInError.message?.contains('ApiException: 10') == true) {
                print('   🚨 DEVELOPER_ERROR (Code 10) - Các nguyên nhân có thể:');
                print('      1. SHA-1 fingerprint chưa được thêm vào Google Cloud Console');
                print('      2. Sai Client ID cho platform Android');
                print('      3. Package name không khớp');
                print('      4. Google Play Services chưa được cập nhật');
                print('   📋 Cấu hình hiện tại:');
                print('      - Package: com.minhduc.naugiday');
                print('      - Client ID: ${GoogleConfig.iosClientId}');
                print('      - SHA-1: 58:D9:28:D4:73:BA:77:86:C4:9C:1B:7F:BF:A2:80:BF:A3:5D:F5:1F');
              }
              break;
            case 'network_error':
              print('   🌐 NETWORK_ERROR: Kiểm tra kết nối internet');
              break;
            case 'sign_in_canceled':
              print('   ❌ SIGN_IN_CANCELED: User đã hủy đăng nhập');
              break;
            default:
              print('   ❓ Unknown error code: ${signInError.code}');
          }
        }

        throw signInError;
      }
      if (googleUser == null) {
        print('   ❌ User cancelled Google sign in');
        return {
          'success': false,
          'message': 'Đăng nhập bằng Google bị hủy',
          'debug': 'Google sign in cancelled by user'
        };
      }

      print('   ✅ Google sign in successful');
      print('   ✓ User email: ${googleUser.email}');
      print('   ✓ User name: ${googleUser.displayName}');

      // 3. Lấy authentication tokens
      print('📋 STEP 3: Lấy authentication tokens');
      print('   ⏳ Đang lấy authentication tokens...');

      final googleAuth = await googleUser.authentication;
      final accessToken = googleAuth.accessToken;
      final idToken = googleAuth.idToken;

      print('   ✓ Access token: ${accessToken != null ? "OK" : "NULL"}');
      print('   ✓ ID token: ${idToken != null ? "OK" : "NULL"}');

      if (accessToken == null) {
        throw 'No Access Token found.';
      }
      if (idToken == null) {
        throw 'No ID Token found.';
      }

      // 4. Đăng nhập với Supabase sử dụng ID token
      print('� STEP 4: Đăng nhập với Supabase');
      print('   ⏳ Đang gọi signInWithIdToken...');
      print('   🔍 Debug token info:');
      print('      - ID Token length: ${idToken.length}');
      print('      - Access Token length: ${accessToken.length}');
      print('      - Web Client ID: ${GoogleConfig.webClientId}');
      print('      - iOS Client ID: ${GoogleConfig.iosClientId}');

      final authResponse = await _supabaseClient.auth.signInWithIdToken(
        provider: OAuthProvider.google,
        idToken: idToken,
        accessToken: accessToken,
      );

      print('   ✅ Supabase sign in successful');
      print('   ✓ User ID: ${authResponse.user?.id}');
      print('   ✓ User email: ${authResponse.user?.email}');
      print('   ✓ Session exists: ${authResponse.session != null}');

      // 5. Lưu thông tin user
      print('� STEP 5: Lưu thông tin user');
      if (authResponse.user != null) {
        await _saveUserDataAfterAuth(authResponse.user!);
      }

      // 6. Trả về kết quả thành công
      print('📋 STEP 6: Trả về kết quả thành công');
      final result = {
        'success': true,
        'userData': {
          'uid': authResponse.user?.id,
          'email': authResponse.user?.email,
          'displayName': authResponse.user?.userMetadata?['full_name'],
          'photoURL': authResponse.user?.userMetadata?['avatar_url'],
        },
        'message': 'Đăng nhập bằng Google thành công',
        'debug': 'signInWithIdToken successful'
      };

      print('   ✅ Returning success result: $result');
      return result;

    } catch (e, stackTrace) {
      print('� =================================');
      print('� LỖI TRONG GOOGLE SIGN IN');
      print('� =================================');
      print('❌ Exception: $e');
      print('❌ Exception type: ${e.runtimeType}');
      print('❌ Stack trace:');
      print(stackTrace.toString());

      String message;
      if (e.toString().contains('network') || e.toString().contains('connection')) {
        message = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.';
      } else if (e.toString().contains('canceled') || e.toString().contains('cancelled')) {
        message = 'Đăng nhập đã bị hủy. Vui lòng thử lại.';
      } else if (e.toString().contains('No Access Token') || e.toString().contains('No ID Token')) {
        message = 'Không thể lấy thông tin xác thực từ Google. Vui lòng thử lại.';
      } else {
        message = 'Đã xảy ra lỗi khi đăng nhập bằng Google. Vui lòng thử lại sau.';
      }

      final errorResult = {
        'success': false,
        'message': message,
        'debug': 'Google sign in error',
        'error': e.toString(),
        'errorType': e.runtimeType.toString()
      };

      print('   ❌ Returning error result: $errorResult');
      return errorResult;

    } finally {
      print('🏁 =================================');
      print('🏁 KẾT THÚC GOOGLE SIGN IN');
      print('🏁 =================================');
    }
  }

  // Helper method để lưu user data sau khi auth thành công
  Future<void> _saveUserDataAfterAuth(User user) async {
    try {
      print('   📋 Saving user data to SharedPreferences...');

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_email', user.email ?? '');
      await prefs.setString('user_id', user.id);
      await prefs.setString('display_name', user.userMetadata?['full_name'] ?? '');
      await prefs.setString('photo_url', user.userMetadata?['avatar_url'] ?? '');
      await prefs.setBool('rememberLogin', true);

      print('   ✅ User data saved successfully');
      print('      - Email: ${user.email}');
      print('      - Name: ${user.userMetadata?['full_name']}');
      print('      - Photo: ${user.userMetadata?['avatar_url']}');
    } catch (e) {
      print('   ❌ Error saving user data: $e');
    }
  }

  Future<Map<String, dynamic>> getCurrentUserData() async {
    if (currentUser == null) {
      return {'success': false, 'message': 'Người dùng chưa đăng nhập'};
    }

    try {
      return {
        'success': true,
        'user': currentUser,
        'userData': {
          'uid': currentUser!.id,
          'email': currentUser!.email,
          'displayName': currentUser!.userMetadata?['full_name'],
          'photoURL': currentUser!.userMetadata?['avatar_url'],
        },
        'message': 'Lấy dữ liệu người dùng thành công'
      };
    } catch (e) {
      print('Lỗi khi lấy dữ liệu người dùng: $e');
      return {
        'success': false,
        'message': 'Đã xảy ra lỗi khi lấy dữ liệu người dùng'
      };
    }
  }

  Future<void> rememberLogin(bool remember) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('rememberLogin', remember);
    } catch (e) {
      print('Lỗi khi lưu trạng thái remember login: $e');
    }
  }

  Future<bool> isRememberLoginEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('rememberLogin') ?? false;
    } catch (e) {
      print('Lỗi khi đọc trạng thái remember login: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>> signInWithEmailAndPassword(
      String email, String password) async {
    print('=== BẮT ĐẦU ĐĂNG NHẬP ===');

    try {
      // Kiểm tra tính hợp lệ của email
      if (!_isValidEmail(email)) {
        print('Email không hợp lệ');
        return {'success': false, 'message': 'Email không hợp lệ'};
      }

      print('Đang đăng nhập với email: $email');

      try {
        // Đăng nhập với Supabase
        final AuthResponse response =
            await _supabaseClient.auth.signInWithPassword(
          email: email,
          password: password,
        );

        // Lấy thông tin user từ response
        final user = response.user;
        if (user == null) {
          print('Đăng nhập thất bại: không lấy được thông tin người dùng');
          return {'success': false, 'message': 'Đăng nhập thất bại'};
        }

        print('Đăng nhập thành công. UserID: ${user.id}');

        // Lưu thông tin vào SharedPreferences
        try {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('user_email', user.email ?? '');
          await prefs.setString('user_id', user.id);
          await prefs.setString(
              'display_name', user.userMetadata?['full_name'] ?? '');
          await prefs.setString(
              'photo_url', user.userMetadata?['avatar_url'] ?? '');
          await prefs.setBool('rememberLogin', true);
          print('Đã lưu thông tin người dùng vào SharedPreferences');
        } catch (e) {
          print(
              'Lỗi khi lưu SharedPreferences (không ảnh hưởng đến đăng nhập): $e');
        }

        // Tạo map userData với kiểu dữ liệu rõ ràng
        final Map<String, dynamic> userData = {
          'uid': user.id,
          'email': user.email,
          'displayName': user.userMetadata?['full_name'],
          'photoURL': user.userMetadata?['avatar_url'],
        };

        return {
          'success': true,
          'userData': userData,
          'message': 'Đăng nhập thành công'
        };
      } catch (e) {
        print('Lỗi đăng nhập với Supabase: $e');

        String message;
        if (e is AuthException) {
          switch (e.message) {
            case 'Invalid login credentials':
              message = 'Email hoặc mật khẩu không chính xác.';
              break;
            case 'Email not confirmed':
              message =
                  'Email chưa được xác nhận. Vui lòng kiểm tra hộp thư để xác nhận.';
              break;
            default:
              message = 'Đăng nhập không thành công: ${e.message}';
          }
        } else if (e.toString().contains('network')) {
          message =
              'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.';
        } else {
          message = 'Đã xảy ra lỗi khi đăng nhập. Vui lòng thử lại sau.';
        }

        return {'success': false, 'message': message};
      }
    } catch (e) {
      print('Lỗi ngoại lệ: $e');
      return {
        'success': false,
        'message':
            'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau hoặc liên hệ hỗ trợ.'
      };
    } finally {
      print('=== KẾT THÚC ĐĂNG NHẬP ===');
    }
  }

  Future<Map<String, dynamic>> createUserWithEmailAndPassword(
      String email, String password) async {
    print('=== BẮT ĐẦU ĐĂNG KÝ TÀI KHOẢN ===');

    try {
      // Kiểm tra tính hợp lệ của email và mật khẩu
      if (!_isValidEmail(email)) {
        print('Email không hợp lệ');
        return {'success': false, 'message': 'Email không hợp lệ'};
      }

      if (!_isValidPassword(password)) {
        print('Mật khẩu không đủ mạnh');
        return {
          'success': false,
          'message': 'Mật khẩu phải có ít nhất 6 ký tự'
        };
      }

      print('Bắt đầu quá trình đăng ký với email: $email');

      try {
        // Tạo tài khoản với Supabase
        final AuthResponse response = await _supabaseClient.auth.signUp(
          email: email,
          password: password,
        );

        // Lấy thông tin user từ response
        final user = response.user;
        if (user == null) {
          print('Đăng ký thất bại: không lấy được thông tin người dùng');
          return {
            'success': false,
            'message': 'Đăng ký thất bại: Không thể lấy thông tin người dùng'
          };
        }

        print('Đã tạo tài khoản thành công với UID: ${user.id}');

        // Lưu một số thông tin cơ bản vào SharedPreferences
        try {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('user_email', email);
          await prefs.setString('user_id', user.id);
          await prefs.setString('created_at', DateTime.now().toIso8601String());

          // Lưu danh sách món ăn yêu thích trống
          await prefs.setStringList('favorite_dishes', []);

          print('Đã lưu thông tin cơ bản của người dùng vào SharedPreferences');
        } catch (prefError) {
          print('Lỗi khi lưu thông tin vào SharedPreferences: $prefError');
        }

        // Trả về kết quả thành công
        return {
          'success': true,
          'userData': {
            'uid': user.id,
            'email': email,
          },
          'message': 'Đăng ký thành công'
        };
      } catch (e) {
        print('Lỗi khi đăng ký tài khoản: $e');

        String message;
        if (e is AuthException) {
          switch (e.message) {
            case 'User already registered':
              message = 'Email này đã được sử dụng bởi tài khoản khác';
              break;
            case 'Password should be at least 6 characters':
              message = 'Mật khẩu phải có ít nhất 6 ký tự';
              break;
            default:
              message = 'Đăng ký thất bại: ${e.message}';
          }
        } else {
          message = 'Đã xảy ra lỗi khi đăng ký. Vui lòng thử lại sau.';
        }

        return {'success': false, 'message': message};
      }
    } catch (e) {
      print('Lỗi không xác định: $e');
      print('Loại lỗi: ${e.runtimeType}');
      print('Chi tiết: ${e.toString()}');

      return {
        'success': false,
        'message': 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.'
      };
    } finally {
      print('=== KẾT THÚC ĐĂNG KÝ TÀI KHOẢN ===');
    }
  }

  Future<void> signOut() async {
    try {
      print('🚪 Bắt đầu đăng xuất từ SupabaseAuthService...');

      // Dừng tất cả các background operations trước
      await _stopBackgroundOperations();

      // Thực hiện đăng xuất với timeout
      await _supabaseClient.auth.signOut().timeout(
        const Duration(seconds: 8),
        onTimeout: () {
          print('⚠️ Timeout khi đăng xuất từ Supabase, tiếp tục với cleanup');
          // Không throw error, chỉ log và tiếp tục
        },
      );

      // Xóa setting "remember login" khi đăng xuất thủ công
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('rememberLogin', false);
      print('✅ Đã xóa setting "remember login" khi đăng xuất');

      print('✅ Đăng xuất thành công từ SupabaseAuthService');
    } catch (e) {
      print('❌ Lỗi khi đăng xuất từ SupabaseAuthService: $e');

      // Vẫn thực hiện cleanup local dù có lỗi
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('rememberLogin', false);
        print('✅ Đã cleanup local data dù có lỗi đăng xuất');
      } catch (cleanupError) {
        print('❌ Lỗi khi cleanup local data: $cleanupError');
      }

      // Không rethrow để tránh crash app
      // rethrow;
    }
  }

  // Dừng các background operations
  Future<void> _stopBackgroundOperations() async {
    try {
      print('🛑 Dừng background operations...');

      // Hủy bỏ các pending requests nếu có
      // Đợi một chút để các operation hiện tại hoàn thành
      await Future.delayed(const Duration(milliseconds: 300));

      print('✅ Đã dừng background operations');
    } catch (e) {
      print('⚠️ Lỗi khi dừng background operations: $e');
    }
  }

  Future<Map<String, dynamic>> resetPassword(String email) async {
    try {
      if (!_isValidEmail(email)) {
        return {'success': false, 'message': 'Email không hợp lệ'};
      }

      await _supabaseClient.auth.resetPasswordForEmail(email);
      return {
        'success': true,
        'message':
            'Đã gửi email đặt lại mật khẩu. Vui lòng kiểm tra hộp thư của bạn.'
      };
    } catch (e) {
      print('Lỗi đặt lại mật khẩu: $e');

      String message;
      if (e is AuthException) {
        switch (e.message) {
          case 'User not found':
            message = 'Không tìm thấy tài khoản với email này';
            break;
          default:
            message = 'Không thể gửi email đặt lại mật khẩu: ${e.message}';
        }
      } else {
        message = 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.';
      }

      return {'success': false, 'message': message};
    }
  }

  // Quản lý món ăn yêu thích sử dụng SharedPreferences như trước
  Future<List<String>> getFavoriteDishes() async {
    if (currentUser == null) return [];

    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getStringList('favorite_dishes') ?? [];
    } catch (e) {
      print('Lỗi lấy danh sách món ăn yêu thích: $e');
      return [];
    }
  }

  Future<void> addFavoriteDish(String dishId) async {
    if (currentUser == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> favorites = prefs.getStringList('favorite_dishes') ?? [];

      if (!favorites.contains(dishId)) {
        favorites.add(dishId);
        await prefs.setStringList('favorite_dishes', favorites);
      }
    } catch (e) {
      print('Lỗi thêm món ăn yêu thích: $e');
    }
  }

  Future<void> removeFavoriteDish(String dishId) async {
    if (currentUser == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> favorites = prefs.getStringList('favorite_dishes') ?? [];

      favorites.remove(dishId);
      await prefs.setStringList('favorite_dishes', favorites);
    } catch (e) {
      print('Lỗi xóa món ăn yêu thích: $e');
    }
  }

  // ===== EMAIL EXISTENCE CHECK =====

  /// Check xem email đã tồn tại trong hệ thống chưa
  /// Ngày tạo: 2024-12-22
  /// Cập nhật: 2024-12-22 - Sử dụng Database RPC Function theo khuyến nghị Supabase
  Future<Map<String, dynamic>> checkEmailExists(String email) async {
    print('=== BẮT ĐẦU CHECK EMAIL TỒN TẠI ===');

    try {
      // Kiểm tra tính hợp lệ của email trước
      if (!_isValidEmail(email)) {
        print('❌ Email không hợp lệ: $email');
        return {
          'exists': false, 
          'valid': false, 
          'message': 'Email không hợp lệ',
          'timestamp': DateTime.now().toIso8601String()
        };
      }

      print('ℹ️ Checking email existence với Database RPC: $email');

      // Gọi Database RPC Function để check user existence
      // Function này cần được tạo trong Supabase SQL Editor trước:
      // CREATE OR REPLACE FUNCTION check_user_exists(user_email text)
      // RETURNS boolean AS $$
      // BEGIN
      //   RETURN EXISTS (
      //     SELECT 1 FROM auth.users 
      //     WHERE email = user_email
      //   );
      // END;
      // $$ LANGUAGE plpgsql SECURITY DEFINER;
      
      final result = await _supabaseClient.rpc('check_user_exists', 
        params: {'user_email': email}
      );

      final bool exists = result == true;
      
      print('✅ Email existence check result: $exists for $email');
      
      return {
        'exists': exists,
        'valid': true,
        'message': exists ? 'Email đã tồn tại trong hệ thống' : 'Email chưa được đăng ký',
        'timestamp': DateTime.now().toIso8601String()
      };
      
    } catch (e) {
      print('❌ Lỗi khi check email existence: $e');
      print('🔍 Chi tiết lỗi: ${e.toString()}');
      
      // Nếu function chưa tồn tại hoặc có lỗi RPC, fallback về check bằng OTP flow
      print('⚠️ Fallback: Không thể check email existence, để OTP flow handle');
      
      return {
        'exists': null,
        'valid': true,
        'message': 'Không thể kiểm tra email, sẽ thử gửi OTP',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String()
      };
    } finally {
      print('=== KẾT THÚC CHECK EMAIL TỒN TẠI ===');
    }
  }

  // ===== OTP AUTHENTICATION METHODS =====

  /// Gửi OTP đến email để đăng ký tài khoản mới
  /// Ngày tạo: 2024-12-19
  /// Cập nhật: 2024-12-22 - Fix shouldCreateUser parameter để detect email đã tồn tại
  Future<Map<String, dynamic>> sendOtpForSignUp(String email) async {
    print('=== BẮT ĐẦU GỬI OTP ĐĂNG KÝ ===');

    try {
      // Kiểm tra tính hợp lệ của email
      if (!_isValidEmail(email)) {
        print('❌ Email không hợp lệ: $email');
        return {'success': false, 'message': 'Email không hợp lệ'};
      }

      print('ℹ️ Gửi OTP đến email: $email');

      // Gửi OTP qua Supabase Auth cho signup flow
      // Sử dụng default behavior để detect email đã tồn tại
      await _supabaseClient.auth.signInWithOtp(
        email: email,
        emailRedirectTo: null, // Không cần redirect cho OTP
      );

      print('✅ Đã gửi OTP thành công đến: $email');
      return {
        'success': true,
        'message':
            'Mã OTP đã được gửi đến email của bạn. Vui lòng kiểm tra hộp thư.',
        'email': email,
        'timestamp': DateTime.now().toIso8601String()
      };
    } on AuthException catch (e) {
      print('❌ Lỗi AuthException khi gửi OTP: ${e.message}');
      print('🔍 Chi tiết lỗi: ${e.toString()}');

      String message;
      String errorCode = 'AUTH_ERROR';
      int? retryAfter;
      
      // Xử lý các trường hợp lỗi cụ thể
      final errorMsg = e.message.toLowerCase();
      
      if (errorMsg.contains('user already registered') || 
          errorMsg.contains('already been registered') ||
          errorMsg.contains('user with this email address has already been registered')) {
        print('📧 Email đã tồn tại trong hệ thống: $email');
        message = 'Email này đã có tài khoản. Vui lòng đăng nhập hoặc đặt lại mật khẩu.';
        errorCode = 'EMAIL_ALREADY_EXISTS';
      } else if (errorMsg.contains('invalid email')) {
        message = 'Email không hợp lệ. Vui lòng kiểm tra lại định dạng email.';
        errorCode = 'INVALID_EMAIL';
      } else if (errorMsg.contains('rate limit') || errorMsg.contains('too many requests')) {
        print('⚠️ Đạt giới hạn rate limit cho email: $email');
        message = 'Bạn đã gửi quá nhiều yêu cầu. Vui lòng đợi 5 phút trước khi thử lại.';
        errorCode = 'EMAIL_RATE_LIMIT_EXCEEDED';
        retryAfter = 300; // 5 phút
      } else if (errorMsg.contains('too many emails')) {
        print('📧 Quá nhiều email được gửi: $email');
        message = 'Quá nhiều email đã được gửi. Vui lòng đợi ít nhất 1 phút trước khi thử lại.';
        errorCode = 'EMAIL_RATE_LIMIT_EXCEEDED';
        retryAfter = 60; // 1 phút
      } else if (errorMsg.contains('signup is disabled')) {
        message = 'Đăng ký tài khoản hiện tại đang được tạm dừng. Vui lòng thử lại sau.';
        errorCode = 'SIGNUP_DISABLED';
      } else {
        // Lỗi chung, sử dụng message gốc từ Supabase nhưng translate sang tiếng Việt
        message = _translateErrorMessage(e.message);
        errorCode = 'AUTH_ERROR';
      }

      final Map<String, dynamic> result = {
        'success': false,
        'message': message,
        'errorCode': errorCode,
        'timestamp': DateTime.now().toIso8601String()
      };

      // Thêm retryAfter nếu có
      if (retryAfter != null) {
        result['retryAfter'] = retryAfter;
      }

      return result;
    } catch (e) {
      print('❌ Lỗi không xác định khi gửi OTP: $e');
      print('🔍 Loại lỗi: ${e.runtimeType}');

      String message;
      if (e.toString().contains('network') ||
          e.toString().contains('connection') ||
          e.toString().contains('timeout')) {
        message =
            'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.';
      } else {
        message = 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.';
      }

      return {
        'success': false,
        'message': message,
        'error': e.toString(),
        'errorCode': 'UNKNOWN_ERROR',
        'timestamp': DateTime.now().toIso8601String()
      };
    } finally {
      print('=== KẾT THÚC GỬI OTP ĐĂNG KÝ ===');
    }
  }

  /// Xác minh OTP và hoàn tất đăng ký
  /// Ngày tạo: 2024-12-19
  Future<Map<String, dynamic>> verifyOtpAndSignUp(
      String email, String otp) async {
    print('=== BẮT ĐẦU XÁC MINH OTP ĐĂNG KÝ ===');

    try {
      // Kiểm tra tính hợp lệ của input
      if (!_isValidEmail(email)) {
        print('❌ Email không hợp lệ: $email');
        return {'success': false, 'message': 'Email không hợp lệ'};
      }

      if (otp.trim().isEmpty || otp.trim().length != 6) {
        print('❌ OTP không hợp lệ: độ dài ${otp.length}');
        return {'success': false, 'message': 'Mã OTP phải có 6 chữ số'};
      }

      print('ℹ️ Xác minh OTP cho email: $email');

      // Xác minh OTP với Supabase
      final AuthResponse response = await _supabaseClient.auth.verifyOTP(
        type: OtpType.email,
        token: otp.trim(),
        email: email,
      );

      // Kiểm tra kết quả xác minh
      final user = response.user;
      if (user == null) {
        print('❌ Xác minh OTP thất bại: không lấy được thông tin người dùng');
        return {
          'success': false,
          'message': 'Xác minh OTP thất bại. Vui lòng thử lại.'
        };
      }

      print('✅ Xác minh OTP thành công. UserID: ${user.id}');

      // Lưu thông tin người dùng vào SharedPreferences
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_email', user.email ?? '');
        await prefs.setString('user_id', user.id);
        await prefs.setString(
            'display_name', user.userMetadata?['full_name'] ?? '');
        await prefs.setString(
            'photo_url', user.userMetadata?['avatar_url'] ?? '');
        await prefs.setBool('rememberLogin', true);
        await prefs.setString('created_at', DateTime.now().toIso8601String());

        // Lưu danh sách món ăn yêu thích trống
        await prefs.setStringList('favorite_dishes', []);

        print('✅ Đã lưu thông tin người dùng vào SharedPreferences');
      } catch (prefError) {
        print(
            '⚠️ Lỗi khi lưu SharedPreferences (không ảnh hưởng đến đăng ký): $prefError');
      }

      // Tạo userData response
      final Map<String, dynamic> userData = {
        'uid': user.id,
        'email': user.email,
        'displayName': user.userMetadata?['full_name'],
        'photoURL': user.userMetadata?['avatar_url'],
      };

      return {
        'success': true,
        'userData': userData,
        'message': 'Đăng ký thành công! Chào mừng bạn đến với CookSpark.',
        'timestamp': DateTime.now().toIso8601String()
      };
    } on AuthException catch (e) {
      print('❌ Lỗi AuthException khi xác minh OTP: ${e.message}');

      String message;
      switch (e.message) {
        case 'Invalid token':
        case 'Token has expired':
          message = 'Mã OTP không chính xác hoặc đã hết hạn. Vui lòng thử lại.';
          break;
        case 'Email not confirmed':
          message = 'Email chưa được xác nhận. Vui lòng kiểm tra lại mã OTP.';
          break;
        default:
          message = 'Xác minh OTP thất bại: ${e.message}';
      }

      return {
        'success': false,
        'message': message,
        'errorCode': 'OTP_VERIFICATION_ERROR',
        'timestamp': DateTime.now().toIso8601String()
      };
    } catch (e) {
      print('❌ Lỗi không xác định khi xác minh OTP: $e');

      return {
        'success': false,
        'message': 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String()
      };
    } finally {
      print('=== KẾT THÚC XÁC MINH OTP ĐĂNG KÝ ===');
    }
  }

  /// Gửi lại OTP (resend)
  /// Ngày tạo: 2024-12-19
  Future<Map<String, dynamic>> resendOtp(String email) async {
    print('=== BẮT ĐẦU GỬI LẠI OTP ===');

    try {
      // Kiểm tra tính hợp lệ của email
      if (!_isValidEmail(email)) {
        print('❌ Email không hợp lệ: $email');
        return {'success': false, 'message': 'Email không hợp lệ'};
      }

      print('ℹ️ Gửi lại OTP đến email: $email');

      // Gửi lại OTP qua Supabase Auth
      await _supabaseClient.auth.resend(
        type: OtpType.email,
        email: email,
      );

      print('✅ Đã gửi lại OTP thành công đến: $email');
      return {
        'success': true,
        'message': 'Mã OTP mới đã được gửi đến email của bạn.',
        'email': email,
        'timestamp': DateTime.now().toIso8601String()
      };
    } on AuthException catch (e) {
      print('❌ Lỗi AuthException khi gửi lại OTP: ${e.message}');

      String message;
      switch (e.message) {
        case 'Rate limit exceeded':
          message =
              'Bạn đã gửi quá nhiều yêu cầu. Vui lòng đợi 1 phút trước khi thử lại.';
          break;
        default:
          message = 'Không thể gửi lại mã OTP: ${e.message}';
      }

      return {
        'success': false,
        'message': message,
        'errorCode': 'RESEND_ERROR',
        'timestamp': DateTime.now().toIso8601String()
      };
    } catch (e) {
      print('❌ Lỗi không xác định khi gửi lại OTP: $e');

      return {
        'success': false,
        'message': 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String()
      };
    } finally {
      print('=== KẾT THÚC GỬI LẠI OTP ===');
    }
  }

  /// Validation cho OTP input
  /// Ngày tạo: 2024-12-19
  Map<String, dynamic> validateOtpInput(String email, String otp) {
    // Kiểm tra email
    if (!_isValidEmail(email)) {
      return {'isValid': false, 'message': 'Email không hợp lệ'};
    }

    // Kiểm tra OTP
    if (otp.trim().isEmpty) {
      return {'isValid': false, 'message': 'Vui lòng nhập mã OTP'};
    }

    if (otp.trim().length != 6) {
      return {'isValid': false, 'message': 'Mã OTP phải có 6 chữ số'};
    }

    // Kiểm tra OTP chỉ chứa số
    if (!RegExp(r'^[0-9]{6}$').hasMatch(otp.trim())) {
      return {'isValid': false, 'message': 'Mã OTP chỉ được chứa số'};
    }

    return {'isValid': true};
  }

  // ===== OTP PASSWORD RESET METHODS =====

  /// Gửi OTP đến email để đặt lại mật khẩu
  /// Ngày tạo: 2024-12-19
  Future<Map<String, dynamic>> sendOtpForPasswordReset(String email) async {
    print('=== BẮT ĐẦU GỬI OTP ĐẶT LẠI MẬT KHẨU ===');

    try {
      // Kiểm tra tính hợp lệ của email
      if (!_isValidEmail(email)) {
        print('❌ Email không hợp lệ: $email');
        return {'success': false, 'message': 'Email không hợp lệ'};
      }

      print('ℹ️ Gửi OTP đặt lại mật khẩu đến email: $email');

      // Gửi OTP qua Supabase Auth cho password reset
      await _supabaseClient.auth.signInWithOtp(
        email: email,
        shouldCreateUser: false, // Không tạo user mới, chỉ gửi OTP cho user đã tồn tại
      );

      print('✅ Đã gửi OTP đặt lại mật khẩu thành công đến: $email');
      return {
        'success': true,
        'message':
            'Mã OTP đã được gửi đến email của bạn để đặt lại mật khẩu.',
        'email': email,
        'timestamp': DateTime.now().toIso8601String()
      };
    } on AuthException catch (e) {
      print('❌ Lỗi AuthException khi gửi OTP đặt lại mật khẩu: ${e.message}');

      String message;
      switch (e.message) {
        case 'User not found':
          message = 'Không tìm thấy tài khoản với email này.';
          break;
        case 'Invalid email':
          message = 'Email không hợp lệ. Vui lòng kiểm tra lại.';
          break;
        case 'Rate limit exceeded':
          message =
              'Bạn đã gửi quá nhiều yêu cầu. Vui lòng thử lại sau 1 phút.';
          break;
        default:
          message = 'Không thể gửi mã OTP: ${e.message}';
      }

      return {
        'success': false,
        'message': message,
        'errorCode': 'AUTH_ERROR',
        'timestamp': DateTime.now().toIso8601String()
      };
    } catch (e) {
      print('❌ Lỗi không xác định khi gửi OTP đặt lại mật khẩu: $e');

      String message;
      if (e.toString().contains('network') ||
          e.toString().contains('connection')) {
        message =
            'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.';
      } else {
        message = 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.';
      }

      return {
        'success': false,
        'message': message,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String()
      };
    } finally {
      print('=== KẾT THÚC GỬI OTP ĐẶT LẠI MẬT KHẨU ===');
    }
  }

  /// Chỉ xác minh OTP cho việc đặt lại mật khẩu (không đặt lại mật khẩu ngay)
  /// Ngày tạo: 2024-12-19
  Future<Map<String, dynamic>> verifyOtpForPasswordReset(
      String email, String otp) async {
    print('=== BẮT ĐẦU XÁC MINH OTP CHO ĐẶT LẠI MẬT KHẨU ===');

    try {
      // Kiểm tra tính hợp lệ của input
      if (!_isValidEmail(email)) {
        print('❌ Email không hợp lệ: $email');
        return {'success': false, 'message': 'Email không hợp lệ'};
      }

      if (otp.trim().isEmpty || otp.trim().length != 6) {
        print('❌ OTP không hợp lệ: độ dài ${otp.length}');
        return {'success': false, 'message': 'Mã OTP phải có 6 chữ số'};
      }

      print('ℹ️ Xác minh OTP cho email: $email');

      // Xác minh OTP
      final AuthResponse verifyResponse = await _supabaseClient.auth.verifyOTP(
        type: OtpType.email,
        token: otp.trim(),
        email: email,
      );

      // Kiểm tra kết quả xác minh
      final user = verifyResponse.user;
      if (user == null) {
        print('❌ Xác minh OTP thất bại: không lấy được thông tin người dùng');
        return {
          'success': false,
          'message': 'Mã OTP không chính xác hoặc đã hết hạn. Vui lòng thử lại.'
        };
      }

      print('✅ Xác minh OTP thành công cho UserID: ${user.id}');

      return {
        'success': true,
        'message': 'Xác minh OTP thành công.',
        'userId': user.id,
        'timestamp': DateTime.now().toIso8601String()
      };
    } on AuthException catch (e) {
      print('❌ Lỗi AuthException khi xác minh OTP: ${e.message}');

      String message;
      switch (e.message) {
        case 'Invalid token':
        case 'Token has expired':
          message = 'Mã OTP không chính xác hoặc đã hết hạn. Vui lòng thử lại.';
          break;
        case 'Email not confirmed':
          message = 'Email chưa được xác nhận. Vui lòng kiểm tra lại mã OTP.';
          break;
        default:
          message = 'Xác minh OTP thất bại: ${e.message}';
      }

      return {
        'success': false,
        'message': message,
        'errorCode': 'OTP_VERIFICATION_ERROR',
        'timestamp': DateTime.now().toIso8601String()
      };
    } catch (e) {
      print('❌ Lỗi không xác định khi xác minh OTP: $e');

      return {
        'success': false,
        'message': 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String()
      };
    } finally {
      print('=== KẾT THÚC XÁC MINH OTP CHO ĐẶT LẠI MẬT KHẨU ===');
    }
  }

  /// Xác minh OTP và đặt lại mật khẩu mới
  /// Ngày tạo: 2024-12-19
  /// Cập nhật: 2024-12-19 - Cải thiện xử lý lỗi token hết hạn
  Future<Map<String, dynamic>> verifyOtpAndResetPassword(
      String email, String otp, String newPassword) async {
    print('=== BẮT ĐẦU XÁC MINH OTP VÀ ĐẶT LẠI MẬT KHẨU ===');

    try {
      // Kiểm tra tính hợp lệ của input
      if (!_isValidEmail(email)) {
        print('❌ Email không hợp lệ: $email');
        return {
          'success': false, 
          'message': 'Email không hợp lệ',
          'errorCode': 'INVALID_EMAIL'
        };
      }

      if (otp.trim().isEmpty || otp.trim().length != 6) {
        print('❌ OTP không hợp lệ: độ dài ${otp.length}');
        return {
          'success': false, 
          'message': 'Mã OTP phải có 6 chữ số',
          'errorCode': 'INVALID_OTP_FORMAT'
        };
      }

      if (!_isValidPassword(newPassword)) {
        print('❌ Mật khẩu mới không hợp lệ');
        return {
          'success': false,
          'message': 'Mật khẩu phải có ít nhất 6 ký tự',
          'errorCode': 'WEAK_PASSWORD'
        };
      }

      print('ℹ️ Xác minh OTP và đặt lại mật khẩu cho email: $email');

      // Bước 1: Xác minh OTP
      final AuthResponse verifyResponse = await _supabaseClient.auth.verifyOTP(
        type: OtpType.email,
        token: otp.trim(),
        email: email,
      );

      // Kiểm tra kết quả xác minh
      final user = verifyResponse.user;
      if (user == null) {
        print('❌ Xác minh OTP thất bại: không lấy được thông tin người dùng');
        return {
          'success': false,
          'message': 'Mã OTP không chính xác hoặc đã hết hạn. Vui lòng lấy mã OTP mới.',
          'errorCode': 'OTP_RESET_PASSWORD_ERROR'
        };
      }

      print('✅ Xác minh OTP thành công cho UserID: ${user.id}');

      // Bước 2: Kiểm tra session còn hợp lệ không trước khi cập nhật mật khẩu
      try {
        final currentSession = _supabaseClient.auth.currentSession;
        if (currentSession == null) {
          print('❌ Session không hợp lệ sau khi xác minh OTP');
          return {
            'success': false,
            'message': 'Phiên đăng nhập đã hết hạn. Vui lòng lấy mã OTP mới.',
            'errorCode': 'OTP_RESET_PASSWORD_ERROR'
          };
        }

        print('ℹ️ Session hợp lệ, tiến hành cập nhật mật khẩu');
        
        // Bước 3: Cập nhật mật khẩu mới
        await _supabaseClient.auth.updateUser(
          UserAttributes(password: newPassword),
        );

        print('✅ Đã cập nhật mật khẩu mới thành công');

        // Đăng xuất sau khi đặt lại mật khẩu thành công để đảm bảo bảo mật
        try {
          await _supabaseClient.auth.signOut();
          print('✅ Đã đăng xuất sau khi đặt lại mật khẩu');
        } catch (signOutError) {
          print('⚠️ Không thể đăng xuất sau khi đặt lại mật khẩu: $signOutError');
          // Không cần fail toàn bộ quá trình vì mật khẩu đã được đặt lại thành công
        }

        return {
          'success': true,
          'message': 'Đặt lại mật khẩu thành công! Bạn có thể đăng nhập với mật khẩu mới.',
          'timestamp': DateTime.now().toIso8601String()
        };
      } catch (updateError) {
        print('❌ Lỗi khi cập nhật mật khẩu mới: $updateError');
        
        // Kiểm tra nếu là lỗi session hết hạn
        if (updateError.toString().contains('session') || 
            updateError.toString().contains('expired') ||
            updateError.toString().contains('invalid')) {
          return {
            'success': false,
            'message': 'Phiên xác thực đã hết hạn. Vui lòng lấy mã OTP mới.',
            'errorCode': 'OTP_RESET_PASSWORD_ERROR'
          };
        }
        
        return {
          'success': false,
          'message': 'Xác minh OTP thành công nhưng không thể cập nhật mật khẩu. Vui lòng thử lại.',
          'errorCode': 'PASSWORD_UPDATE_ERROR'
        };
      }
    } on AuthException catch (e) {
      print('❌ Lỗi AuthException khi xác minh OTP và đặt lại mật khẩu: ${e.message}');

      String message;
      String errorCode = 'OTP_RESET_PASSWORD_ERROR';
      
      switch (e.message) {
        case 'Invalid token':
          message = 'Mã xác nhận không chính xác. Vui lòng kiểm tra lại hoặc lấy mã mới.';
          break;
        case 'Token has expired':
        case 'Token has expired or is invalid':
          message = 'Mã xác nhận đã hết hiệu lực. Vui lòng lấy mã xác nhận mới để tiếp tục.';
          break;
        case 'Email not confirmed':
          message = 'Email chưa được xác nhận. Vui lòng kiểm tra lại mã xác nhận.';
          break;
        case 'User not found':
          message = 'Không tìm thấy tài khoản với email này.';
          errorCode = 'USER_NOT_FOUND';
          break;
        default:
          message = 'Đặt lại mật khẩu thất bại. Vui lòng thử lại sau.';
      }

      return {
        'success': false,
        'message': message,
        'errorCode': errorCode,
        'timestamp': DateTime.now().toIso8601String()
      };
    } catch (e) {
      print('❌ Lỗi không xác định khi đặt lại mật khẩu: $e');

      String message;
      if (e.toString().contains('network') || 
          e.toString().contains('connection')) {
        message = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.';
      } else {
        message = 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.';
      }

      return {
        'success': false,
        'message': message,
        'error': e.toString(),
        'errorCode': 'UNKNOWN_ERROR',
        'timestamp': DateTime.now().toIso8601String()
      };
    } finally {
      print('=== KẾT THÚC XÁC MINH OTP VÀ ĐẶT LẠI MẬT KHẨU ===');
    }
  }

  /// Gửi lại OTP cho việc đặt lại mật khẩu
  /// Ngày tạo: 2024-12-19
  Future<Map<String, dynamic>> resendOtpForPasswordReset(String email) async {
    print('=== BẮT ĐẦU GỬI LẠI OTP ĐẶT LẠI MẬT KHẨU ===');

    try {
      // Kiểm tra tính hợp lệ của email
      if (!_isValidEmail(email)) {
        print('❌ Email không hợp lệ: $email');
        return {'success': false, 'message': 'Email không hợp lệ'};
      }

      print('ℹ️ Gửi lại OTP đặt lại mật khẩu đến email: $email');

      // Gửi lại OTP qua Supabase Auth
      await _supabaseClient.auth.resend(
        type: OtpType.email,
        email: email,
      );

      print('✅ Đã gửi lại OTP đặt lại mật khẩu thành công đến: $email');
      return {
        'success': true,
        'message': 'Mã OTP mới đã được gửi đến email của bạn.',
        'email': email,
        'timestamp': DateTime.now().toIso8601String()
      };
    } on AuthException catch (e) {
      print('❌ Lỗi AuthException khi gửi lại OTP đặt lại mật khẩu: ${e.message}');

      String message;
      switch (e.message) {
        case 'Rate limit exceeded':
          message =
              'Bạn đã gửi quá nhiều yêu cầu. Vui lòng đợi 1 phút trước khi thử lại.';
          break;
        default:
          message = 'Không thể gửi lại mã OTP: ${e.message}';
      }

      return {
        'success': false,
        'message': message,
        'errorCode': 'RESEND_ERROR',
        'timestamp': DateTime.now().toIso8601String()
      };
    } catch (e) {
      print('❌ Lỗi không xác định khi gửi lại OTP đặt lại mật khẩu: $e');

      return {
        'success': false,
        'message': 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String()
      };
    } finally {
      print('=== KẾT THÚC GỬI LẠI OTP ĐẶT LẠI MẬT KHẨU ===');
    }
  }
}
